import 'dart:async';
import 'package:easydine_main/widgets/app_bar.dart';
import 'package:easydine_main/widgets/tiled_background.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:ui' as ui;
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/running_orders/running_orders_bloc.dart';
import '../../blocs/running_orders/running_orders_event.dart';
import '../../blocs/running_orders/running_orders_state.dart';
import '../../blocs/cart/cart_bloc.dart';
import '../../blocs/cart/cart_event.dart';
import '../../blocs/cart/cart_state.dart';
import '../../router/router_constants.dart';
import '../../services/order_service.dart';
import '../../services/thermal_print_service.dart';
import '../../models/order_model.dart';

import '../../widgets/running_order_card.dart';
import '../../widgets/order_status_dropdown.dart' as dropdown;

// Order Status Enum matching backend
enum OrderStatus {
  PENDING('PENDING', 'Pending', Colors.orange),
  IN_PREPARATION('IN_PREPARATION', 'In Preparation', Colors.blue),
  READY('READY', 'Ready', Colors.green),
  SERVED('SERVED', 'Served', Colors.purple),
  CANCELLED('CANCELLED', 'Cancelled', Colors.red),
  CHECKOUT('CHECKOUT', 'Checkout', Colors.teal),
  COMPLETED('COMPLETED', 'Completed', Colors.grey);

  const OrderStatus(this.value, this.displayName, this.color);
  final String value;
  final String displayName;
  final Color color;

  static OrderStatus fromString(String status) {
    return OrderStatus.values.firstWhere(
      (e) => e.value == status.toUpperCase(),
      orElse: () => OrderStatus.PENDING,
    );
  }
}

// Import the RunningOrderCard widget
// import 'running_order_card.dart';

class RunningOrdersPage extends StatefulWidget {
  @override
  _RunningOrdersPageState createState() => _RunningOrdersPageState();
}

class _RunningOrdersPageState extends State<RunningOrdersPage>
    with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  late RunningOrdersBloc _runningOrdersBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this, initialIndex: 0);
    _runningOrdersBloc = RunningOrdersBloc();

    // Add listener for tab changes
    _tabController.addListener(_onTabChanged);

    // Fetch initial orders
    _runningOrdersBloc.add(FetchRunningOrders());

    debugPrint(
        '🔄 TabController initialized with index: ${_tabController.index}');
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _runningOrdersBloc.close();
    super.dispose();
  }

  void _onTabChanged() {
    if (!_tabController.indexIsChanging) return;

    final category = _getTabCategory(_tabController.index);
    debugPrint('🔄 Tab changed to: $category (index: ${_tabController.index})');

    _runningOrdersBloc.add(FilterByCategory(category));
  }

  String _getTabCategory(int index) {
    switch (index) {
      case 0:
        return "All";
      case 1:
        return "Dine In";
      case 2:
        return "Takeaway";
      case 3:
        return "Delivery";
      default:
        return "All";
    }
  }

  void _handleOrderTap(Map<String, dynamic> order, BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(
        maxWidth: isLandscape
            ? MediaQuery.of(context).size.width * 0.85
            : MediaQuery.of(context).size.width * 0.75,
        maxHeight: MediaQuery.of(context).orientation == Orientation.portrait
            ? MediaQuery.of(context).size.height * 0.75
            : MediaQuery.of(context).size.height * 0.9,
      ),
      builder: (context) => SafeArea(
        child: Container(
          margin: isLandscape
              ? EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                )
              : null,
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20),
              bottom: isLandscape ? Radius.circular(20) : Radius.zero,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (!isLandscape)
                Container(
                  width: 40,
                  height: 4,
                  margin: EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[600],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              if (isLandscape)
                Align(
                  alignment: Alignment.topRight,
                  child: IconButton(
                    icon: Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                    padding: EdgeInsets.all(16),
                  ),
                ),
              Expanded(
                child: OrderDetailsModal(order: order),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleLoadToCart(Map<String, dynamic> order, BuildContext context) {
    try {
      debugPrint('🛒 Loading order ${order['orderDetailId']} into cart');

      // Get the cart bloc and load the order data
      final cartBloc = context.read<CartBloc>();
      final navigator = GoRouter.of(context);
      final scaffoldMessenger = ScaffoldMessenger.of(context);

      // Listen for cart state changes to handle activation and navigation
      late StreamSubscription cartSubscription;
      cartSubscription = cartBloc.stream.listen((cartState) {
        if (cartState is CartLoaded) {
          // Cart loaded successfully, now activate it and navigate
          debugPrint(
              '🛒 Cart loaded with ID: ${cartState.currentCart?.cartId}');

          // Activate the cart
          if (cartState.currentCart != null) {
            cartBloc.add(ActivateCart(cartId: cartState.currentCart!.cartId));
          }

          // Cancel subscription to avoid multiple triggers
          cartSubscription.cancel();

          // Navigate to POS screen
          GoRouter.of(context).goNamed(RouterConstants.pos, queryParameters: {
            'tableNumber': order['tableNumber'] ?? '',
            'orderId': order['orderDetailId'] ?? '',
            'orderType': order['type'] ?? 'dine_in',
          });

          // Show success message
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content:
                  Text('Order loaded into cart and activated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (cartState is CartError) {
          // Handle error case
          cartSubscription.cancel();
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content:
                  Text('Failed to load order into cart: ${cartState.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });

      // Load the order data into cart with original order ID for tracking
      final originalOrderId = order['orderDetailId'] as String?;
      cartBloc.add(LoadOrderIntoCart(
        orderData: order,
        originalOrderId: originalOrderId,
      ));
    } catch (e) {
      debugPrint('❌ Error loading order to cart: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load order into cart'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleCancelOrder(Map<String, dynamic> order, BuildContext context) {
    debugPrint("Cancelling${order.toString()}");
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Cancel Order',
          style: GoogleFonts.dmSans(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to cancel order #${order['id']}? This action cannot be undone.',
          style: GoogleFonts.dmSans(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Keep Order',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () async {
              Navigator.pop(context);

              try {
                // Show loading
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Canceling order...'),
                    backgroundColor: Colors.orange,
                  ),
                );

                // Cancel the order
                final success =
                    await OrderService.cancelOrder(order['orderDetailId']);

                if (success) {
                  // Refresh the orders list
                  _runningOrdersBloc.add(RefreshOrders());

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Order canceled successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to cancel order'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                debugPrint('❌ Error canceling order: $e');
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error canceling order'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: Text(
              'Cancel Order',
              style: GoogleFonts.dmSans(),
            ),
          ),
        ],
      ),
    );
  }

  void _handleStatusChange(Map<String, dynamic> order, OrderStatus newStatus,
      BuildContext context) async {
    try {
      debugPrint(
          '🔄 Changing order ${order['orderDetailId']} status to ${newStatus.value}');

      final success = await OrderService.updateOrderStatus(
          order['orderDetailId'], newStatus.value);

      if (success) {
        // Refresh the orders list
        _runningOrdersBloc.add(RefreshOrders());

        // Close the modal
        Navigator.pop(context);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update order status'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error updating order status: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating order status'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleStatusChangeFromCard(
      Map<String, dynamic> order, String newStatus, BuildContext context) {
    // Convert string status to OrderStatus enum
    OrderStatus? orderStatus = _convertStringToOrderStatus(newStatus);
    if (orderStatus != null) {
      // Show confirmation dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Confirm Status Change'),
            content: Text(
                'Are you sure you want to change the order status to ${orderStatus.displayName}?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _handleStatusChange(order, orderStatus, context);
                },
                child: Text('Confirm'),
              ),
            ],
          );
        },
      );
    }
  }

  OrderStatus? _convertStringToOrderStatus(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return OrderStatus.PENDING;
      case 'IN_PREPARATION':
        return OrderStatus.IN_PREPARATION;
      case 'READY':
        return OrderStatus.READY;
      case 'SERVED':
        return OrderStatus.SERVED;
      case 'CANCELLED':
        return OrderStatus.CANCELLED;
      case 'CHECKOUT':
        return OrderStatus.CHECKOUT;
      case 'COMPLETED':
        return OrderStatus.COMPLETED;
      default:
        return null;
    }
  }

  void _handlePrintBill(
      Map<String, dynamic> order, BuildContext context) async {
    try {
      debugPrint('🖨️ Printing bill for order ${order['id']}');

      // Get the original OrderDetail from the stored data
      final originalOrderDetail = order['_originalOrderDetail'] as OrderDetail?;
      if (originalOrderDetail == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Original order data not available for printing',
              style: GoogleFonts.dmSans(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Use the original OrderDetail and get the first bill
      final orderDetail = originalOrderDetail;
      final bill =
          orderDetail.bills.isNotEmpty ? orderDetail.bills.first : null;
      if (bill == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'No bill data available for this order',
              style: GoogleFonts.dmSans(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Create thermal bill data
      final thermalBillData =
          ThermalBillData.fromOrderAndBill(orderDetail, bill);

      // Initialize thermal print service
      final thermalPrintService = ThermalPrintService();

      // Show options dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Text(
            'Print Bill',
            style: GoogleFonts.dmSans(color: Colors.white),
          ),
          content: Text(
            'Choose an option for the bill:',
            style: GoogleFonts.dmSans(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.dmSans(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                await thermalPrintService.printBill(thermalBillData);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Bill sent to printer',
                      style: GoogleFonts.dmSans(),
                    ),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: Text(
                'Print',
                style: GoogleFonts.dmSans(color: Colors.green),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                await thermalPrintService.shareAndSaveBill(thermalBillData);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Bill saved and shared',
                      style: GoogleFonts.dmSans(),
                    ),
                    backgroundColor: Colors.blue,
                  ),
                );
              },
              child: Text(
                'Share',
                style: GoogleFonts.dmSans(color: Colors.blue),
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('❌ Error printing bill: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error printing bill: ${e.toString()}',
            style: GoogleFonts.dmSans(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleRefundOrder(Map<String, dynamic> order, BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.money_off, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              'Refund Order',
              style: GoogleFonts.dmSans(color: Colors.white),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to process a refund for this order?',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order: ${order['id']}',
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Table: ${order['table']}',
                    style: GoogleFonts.dmSans(color: Colors.white70),
                  ),
                  Text(
                    'Amount: \$${order['total']?.toStringAsFixed(2) ?? '0.00'}',
                    style: GoogleFonts.dmSans(
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processRefund(order, context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            child: Text(
              'Process Refund',
              style: GoogleFonts.dmSans(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _processRefund(Map<String, dynamic> order, BuildContext context) {
    // TODO: Implement actual refund processing with backend API
    // For now, show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Refund processed for order ${order['id']}',
          style: GoogleFonts.dmSans(),
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _handleShareBill(
      Map<String, dynamic> order, BuildContext context) async {
    try {
      debugPrint('📤 Sharing bill for order ${order['id']}');

      // Get the original OrderDetail from the stored data
      final originalOrderDetail = order['_originalOrderDetail'] as OrderDetail?;
      if (originalOrderDetail == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Original order data not available for sharing',
              style: GoogleFonts.dmSans(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Use the original OrderDetail and get the first bill
      final orderDetail = originalOrderDetail;
      final bill =
          orderDetail.bills.isNotEmpty ? orderDetail.bills.first : null;
      if (bill == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'No bill data available for this order',
              style: GoogleFonts.dmSans(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Create thermal bill data
      final thermalBillData =
          ThermalBillData.fromOrderAndBill(orderDetail, bill);

      // Initialize thermal print service
      final thermalPrintService = ThermalPrintService();

      // Share the bill
      await thermalPrintService.shareAndSaveBill(thermalBillData);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Bill shared successfully',
            style: GoogleFonts.dmSans(),
          ),
          backgroundColor: Colors.blue,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } catch (e) {
      debugPrint('❌ Error sharing bill: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error sharing bill: ${e.toString()}',
            style: GoogleFonts.dmSans(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _runningOrdersBloc,
      child: Scaffold(
        extendBodyBehindAppBar: true,
        key: _scaffoldKey,
        appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
        body: Stack(
          children: [
            TiledBackground(),
            BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 32, sigmaY: 32),
              child: BlocBuilder<RunningOrdersBloc, RunningOrdersState>(
                bloc: _runningOrdersBloc,
                builder: (context, state) {
                  if (state.status == RunningOrdersStatus.loading) {
                    return Center(child: CircularProgressIndicator());
                  }

                  if (state.status == RunningOrdersStatus.failure) {
                    return Center(
                      child: Text(
                        state.error ?? 'An error occurred',
                        style: TextStyle(color: Colors.red),
                      ),
                    );
                  }

                  final filteredOrders = state.orders;

                  return Column(
                    children: [
                      SizedBox(height: AppBar().preferredSize.height + 30),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: TabBar(
                          controller: _tabController,
                          dividerHeight: 0,
                          onTap: (index) {
                            debugPrint('🔄 Tab tapped: index $index');
                            String category = _getTabCategory(index);
                            debugPrint('🔄 Tab tapped: category $category');
                          },
                          indicator: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          labelStyle: GoogleFonts.dmSans(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          unselectedLabelStyle: GoogleFonts.dmSans(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          labelColor: Colors.white,
                          unselectedLabelColor: Colors.white.withOpacity(0.5),
                          padding: EdgeInsets.all(4),
                          labelPadding: EdgeInsets.symmetric(horizontal: 4),
                          tabs: [
                            _buildTab("All", Icons.list_alt, context),
                            _buildTab("Dine In", Icons.restaurant, context),
                            _buildTab(
                                "Takeaway", Icons.takeout_dining, context),
                            _buildTab(
                                "Delivery", Icons.delivery_dining, context),
                          ],
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.01),
                      // Stats summary with updated styling
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16),
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatItem(
                              "Total",
                              filteredOrders.length.toString(),
                              Colors.blue,
                              Icons.receipt_long,
                            ),
                            _buildStatItem(
                              "Ready",
                              filteredOrders
                                  .where((order) => order["status"] == "Ready")
                                  .length
                                  .toString(),
                              Colors.green,
                              Icons.check_circle,
                            ),
                            _buildStatItem(
                              "In Progress",
                              filteredOrders
                                  .where((order) =>
                                      order["status"] == "In Progress")
                                  .length
                                  .toString(),
                              Colors.amber,
                              Icons.pending,
                            ),
                            _buildStatItem(
                              "Cooking",
                              filteredOrders
                                  .where(
                                      (order) => order["status"] == "Cooking")
                                  .length
                                  .toString(),
                              Colors.orange,
                              Icons.restaurant,
                            ),
                            _buildStatItem(
                              "Cancelled",
                              filteredOrders
                                  .where(
                                      (order) => order["status"] == "Cancelled")
                                  .length
                                  .toString(),
                              Colors.red,
                              Icons.cancel,
                            ),
                            _buildStatItem(
                              "Completed",
                              filteredOrders
                                  .where(
                                      (order) => order["status"] == "Completed")
                                  .length
                                  .toString(),
                              Colors.purple,
                              Icons.done_all,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.01),
                      Expanded(
                        child: filteredOrders.isEmpty
                            ? _buildEmptyState(state.selectedCategory, context)
                            : state.selectedCategory == "All"
                                ? _buildGroupedOrdersList(
                                    filteredOrders, context)
                                : _buildOrdersList(filteredOrders, context),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String selectedCategory, BuildContext context) {
    String message;
    IconData icon;

    switch (selectedCategory) {
      case "All":
        message = "No orders found";
        icon = Icons.assignment_outlined;
        break;
      case "Dine In":
        message = "No dine-in orders found";
        icon = Icons.restaurant;
        break;
      case "Takeaway":
        message = "No takeaway orders found";
        icon = Icons.takeout_dining;
        break;
      case "Delivery":
        message = "No delivery orders found";
        icon = Icons.delivery_dining;
        break;
      default:
        message = "No orders found for this type";
        icon = Icons.assignment_outlined;
    }

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            message,
            style: GoogleFonts.dmSans(
              fontSize: 18,
              color: Colors.grey[300],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            selectedCategory == "All"
                ? "Try refreshing or check back later"
                : "Switch to 'All' to see orders of other types",
            style: GoogleFonts.dmSans(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (selectedCategory == "All") ...[
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                _runningOrdersBloc.add(RefreshOrders());
              },
              icon: Icon(Icons.refresh, size: 18),
              label: Text(
                'Refresh Orders',
                style: GoogleFonts.dmSans(fontSize: 14),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGroupedOrdersList(
      List<Map<String, dynamic>> orders, BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    // Group orders by type
    final Map<String, List<Map<String, dynamic>>> groupedOrders = {
      'Dine In': [],
      'Takeaway': [],
      'Delivery': [],
    };

    for (var order in orders) {
      final type = order['type'] as String;
      groupedOrders[type]?.add(order);
    }

    // Remove empty categories
    groupedOrders.removeWhere((key, value) => value.isEmpty);

    if (isLandscape) {
      return ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: groupedOrders.length,
        itemBuilder: (context, index) {
          final category = groupedOrders.keys.elementAt(index);
          final categoryOrders = groupedOrders[category]!;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
                child: Row(
                  children: [
                    Icon(
                      _getCategoryIcon(category),
                      color: Colors.white70,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      category,
                      style: GoogleFonts.dmSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 8),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${categoryOrders.length}',
                        style: GoogleFonts.dmSans(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              StaggeredGrid.count(
                crossAxisCount: 3,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                children: categoryOrders.map((order) {
                  return InkWell(
                    onTap: () => _handleOrderTap(order, context),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: BackdropFilter(
                          filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                          child: RunningOrderCard(
                            order: order,
                            onLoadToCart: () =>
                                _handleLoadToCart(order, context),
                            onCancelOrder: () =>
                                _handleCancelOrder(order, context),
                            onPrintBill: () => _handlePrintBill(order, context),
                            onRefundOrder: () =>
                                _handleRefundOrder(order, context),
                            onShareBill: () => _handleShareBill(order, context),
                            onStatusChange: (newStatus) =>
                                _handleStatusChangeFromCard(
                                    order, newStatus, context),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          );
        },
      );
    }

    // Portrait mode with grouped orders
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: groupedOrders.length,
      itemBuilder: (context, index) {
        final category = groupedOrders.keys.elementAt(index);
        final categoryOrders = groupedOrders[category]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
              child: Row(
                children: [
                  Icon(
                    _getCategoryIcon(category),
                    color: Colors.white70,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    category,
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: 8),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${categoryOrders.length}',
                      style: GoogleFonts.dmSans(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            ...categoryOrders.map((order) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: InkWell(
                  onTap: () => _handleOrderTap(order, context),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: BackdropFilter(
                        filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: RunningOrderCard(
                          order: order,
                          onLoadToCart: () => _handleLoadToCart(order, context),
                          onCancelOrder: () =>
                              _handleCancelOrder(order, context),
                          onPrintBill: () => _handlePrintBill(order, context),
                          onRefundOrder: () =>
                              _handleRefundOrder(order, context),
                          onShareBill: () => _handleShareBill(order, context),
                          onStatusChange: (newStatus) =>
                              _handleStatusChangeFromCard(
                                  order, newStatus, context),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Dine In':
        return Icons.restaurant;
      case 'Takeaway':
        return Icons.takeout_dining;
      case 'Delivery':
        return Icons.delivery_dining;
      default:
        return Icons.list_alt;
    }
  }

  Widget _buildOrdersList(
      List<Map<String, dynamic>> orders, BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    if (isLandscape) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: StaggeredGrid.count(
          crossAxisCount: 3,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          children: orders.map((order) {
            return InkWell(
              onTap: () => _handleOrderTap(order, context),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: BackdropFilter(
                    filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: RunningOrderCard(
                      order: order,
                      onLoadToCart: () => _handleLoadToCart(order, context),
                      onCancelOrder: () => _handleCancelOrder(order, context),
                      onPrintBill: () => _handlePrintBill(order, context),
                      onRefundOrder: () => _handleRefundOrder(order, context),
                      onShareBill: () => _handleShareBill(order, context),
                      onStatusChange: (newStatus) =>
                          _handleStatusChangeFromCard(
                              order, newStatus, context),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      );
    }

    // Portrait mode - keep the original list view
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: InkWell(
            onTap: () => _handleOrderTap(order, context),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.08),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: BackdropFilter(
                  filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: RunningOrderCard(
                    order: order,
                    onLoadToCart: () => _handleLoadToCart(order, context),
                    onCancelOrder: () => _handleCancelOrder(order, context),
                    onPrintBill: () => _handlePrintBill(order, context),
                    onRefundOrder: () => _handleRefundOrder(order, context),
                    onShareBill: () => _handleShareBill(order, context),
                    onStatusChange: (newStatus) =>
                        _handleStatusChangeFromCard(order, newStatus, context),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTab(String text, IconData icon, BuildContext context) {
    return Tab(
      height:
          MediaQuery.of(context).orientation == Orientation.portrait ? 48 : 36,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 50),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 18),
            SizedBox(width: 8),
            Text(text),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, Color color, IconData icon) {
    return Row(
      children: [
        Container(
          margin: EdgeInsets.only(right: 16),
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: GoogleFonts.dmSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: GoogleFonts.dmSans(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Order Details Modal
class OrderDetailsModal extends StatefulWidget {
  final Map<String, dynamic> order;
  const OrderDetailsModal({super.key, required this.order});

  @override
  State<OrderDetailsModal> createState() => _OrderDetailsModalState();
}

class _OrderDetailsModalState extends State<OrderDetailsModal> {
  String? selectedPaymentMethod;
  double tipAmount = 0.0;
  final tipController = TextEditingController();
  bool isProcessingPayment = false;
  double cashAmount = 0.0;
  double cardAmount = 0.0;
  bool isSplittingBill = false;
  int numberOfBills = 2; // Default to 2 bills
  List<double> billAmounts = <double>[
    0.0,
    0.0
  ]; // Dynamic list for bill amounts
  Map<String, int> billAssignments =
      {}; // 0 for unassigned, 1+ for bill numbers
  List<double> billSubtotals = <double>[
    0.0,
    0.0
  ]; // Dynamic list for bill subtotals

  void _updateBillTotals() {
    // Reset all bill subtotals to match current number of bills
    billSubtotals = List<double>.filled(numberOfBills, 0.0);

    (widget.order['items'] as List).forEach((item) {
      final itemTotal = (item['price'] as double) * (item['quantity'] as int);
      final billIndex = billAssignments[item['id']];
      if (billIndex != null && billIndex > 0 && billIndex <= numberOfBills) {
        billSubtotals[billIndex - 1] += itemTotal;
      }
    });

    // Calculate tax and total for each bill
    billAmounts = List<double>.generate(numberOfBills, (index) {
      final tax = billSubtotals[index] * 0.1;
      return billSubtotals[index] + tax + (tipAmount / numberOfBills);
    });

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
      final price = item['price'] ?? 0.0;
      final quantity = item['quantity'] ?? 1;
      return sum + (price * quantity);
    });
    final tax = subtotal * 0.1;
    final total = subtotal + tax + tipAmount;

    if (isLandscape) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Left side - Order details and items
          Expanded(
            flex: 3,
            child: CustomScrollView(
              slivers: [
                SliverPadding(
                  padding: EdgeInsets.all(20),
                  sliver: SliverToBoxAdapter(
                    child: _buildOrderHeader(),
                  ),
                ),
                SliverPadding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  sliver: SliverToBoxAdapter(
                    child: _buildItemsList(),
                  ),
                ),
                SliverPadding(
                  padding: EdgeInsets.only(bottom: 20),
                ),
              ],
            ),
          ),

          // Vertical divider
          Container(
            width: 1,
            margin: EdgeInsets.symmetric(vertical: 20),
            color: Colors.grey[800],
          ),

          // Right side - Payment and actions
          Expanded(
            flex: 2,
            child: CustomScrollView(
              slivers: [
                SliverPadding(
                  padding: EdgeInsets.all(20),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      _buildPaymentSection(),
                      SizedBox(height: 24),
                      _buildTipSection(subtotal),
                      SizedBox(height: 24),
                      _buildBillSummary(subtotal, tax, total),
                      SizedBox(height: 24),
                      _buildActionButtons(total),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    // Portrait layout
    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: EdgeInsets.all(20),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              _buildOrderHeader(),
              SizedBox(height: 24),
              _buildItemsList(),
              SizedBox(height: 24),
              _buildPaymentSection(),
              SizedBox(height: 24),
              _buildTipSection(subtotal),
              SizedBox(height: 24),
              _buildBillSummary(subtotal, tax, total),
              SizedBox(height: 24),
              _buildActionButtons(total),
              // Add bottom padding for safe area
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ]),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderHeader() {
    final orderid = widget.order['id'];
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  orderid,
                  style: GoogleFonts.dmSans(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  "${widget.order['type']} • Table ${widget.order['table'].toString().split(',').last.replaceAll(')', '')}",
                  style: GoogleFonts.dmSans(
                    color: Colors.grey[400],
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor(widget.order['status']),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                widget.order['status'],
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        // _buildStatusChangeSection(),
      ],
    );
  }

  // Widget _buildStatusChangeSection() {
  //   final currentStatus =
  //       dropdown.OrderStatus.fromString(widget.order['status']);
  //
  //   return Container(
  //     padding: EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //       color: Colors.grey[800],
  //       borderRadius: BorderRadius.circular(12),
  //       border: Border.all(color: Colors.grey[700]!),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           'Change Order Status',
  //           style: GoogleFonts.dmSans(
  //             color: Colors.white,
  //             fontSize: 16,
  //             fontWeight: FontWeight.bold,
  //           ),
  //         ),
  //         SizedBox(height: 12),
  //         dropdown.OrderStatusDropdown(
  //           currentStatus: currentStatus,
  //           onStatusChanged: (newStatus) {
  //             final runningOrdersPage =
  //                 context.findAncestorStateOfType<_RunningOrdersPageState>();
  //             runningOrdersPage?._handleStatusChange(
  //                 widget.order, _convertToLegacyStatus(newStatus), context);
  //           },
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Convert new dropdown status to legacy status for compatibility
  OrderStatus _convertToLegacyStatus(dropdown.OrderStatus dropdownStatus) {
    switch (dropdownStatus) {
      case dropdown.OrderStatus.PENDING:
        return OrderStatus.PENDING;
      case dropdown.OrderStatus.IN_PREPARATION:
        return OrderStatus.IN_PREPARATION;
      case dropdown.OrderStatus.READY:
        return OrderStatus.READY;
      case dropdown.OrderStatus.SERVED:
        return OrderStatus.SERVED;
      case dropdown.OrderStatus.CANCELLED:
        return OrderStatus.CANCELLED;
      case dropdown.OrderStatus.CHECKOUT:
        return OrderStatus.CHECKOUT;
      case dropdown.OrderStatus.COMPLETED:
        return OrderStatus.COMPLETED;
    }
  }

  void _showStatusChangeConfirmation(OrderStatus newStatus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Change Order Status',
          style: GoogleFonts.dmSans(color: Colors.white),
        ),
        content: Text(
          'Change order status to ${newStatus.displayName}?',
          style: GoogleFonts.dmSans(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: newStatus.color,
            ),
            onPressed: () {
              Navigator.pop(context);
              final runningOrdersPage =
                  context.findAncestorStateOfType<_RunningOrdersPageState>();
              runningOrdersPage?._handleStatusChange(
                  widget.order, newStatus, context);
            },
            child: Text(
              'Confirm',
              style: GoogleFonts.dmSans(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isSplittingBill) ...[
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Row(
              children: [
                Text(
                  'Split Bills ($numberOfBills)',
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(width: 12),
                if (numberOfBills < 5)
                  IconButton(
                    icon: Icon(Icons.add_circle, color: Colors.green),
                    onPressed: () {
                      setState(() {
                        numberOfBills++;
                        // Recreate the lists with the new size
                        billAmounts = List<double>.filled(numberOfBills, 0.0);
                        billSubtotals = List<double>.filled(numberOfBills, 0.0);
                        _updateBillTotals();
                      });
                    },
                    tooltip: 'Add Bill',
                  ),
                if (numberOfBills > 2)
                  IconButton(
                    icon: Icon(Icons.remove_circle, color: Colors.red),
                    onPressed: () {
                      setState(() {
                        // Remove assignments for the last bill
                        billAssignments.removeWhere(
                            (key, value) => value == numberOfBills);
                        numberOfBills--;
                        // Recreate the lists with the new size
                        billAmounts = List<double>.filled(numberOfBills, 0.0);
                        billSubtotals = List<double>.filled(numberOfBills, 0.0);
                        _updateBillTotals();
                      });
                    },
                    tooltip: 'Remove Bill',
                  ),
                Spacer(),
                TextButton.icon(
                  icon: Icon(Icons.close, color: Colors.red),
                  label: Text(
                    'Cancel Split',
                    style: GoogleFonts.dmSans(color: Colors.red),
                  ),
                  onPressed: () {
                    setState(() {
                      isSplittingBill = false;
                      numberOfBills = 2;
                      billAmounts = <double>[0.0, 0.0];
                      billSubtotals = <double>[0.0, 0.0];
                      billAssignments.clear();
                      _updateBillTotals();
                    });
                  },
                ),
              ],
            ),
          ),
          StaggeredGrid.count(
            crossAxisCount: numberOfBills > 3 ? 3 : numberOfBills,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            children: List.generate(numberOfBills, (index) {
              final billColors = [
                Colors.blue,
                Colors.green,
                Colors.orange,
                Colors.purple,
                Colors.teal,
              ];
              final color = billColors[index % billColors.length];

              return Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: color.withOpacity(0.5)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Bill ${index + 1}',
                      style: GoogleFonts.dmSans(
                        color: color,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '\$${billSubtotals[index].toStringAsFixed(2)}',
                      style: GoogleFonts.dmSans(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ),
          SizedBox(height: 16),
        ],
        ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.4,
          ),
          child: ListView.builder(
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            physics: AlwaysScrollableScrollPhysics(),
            itemCount: (widget.order['items'] as List).length,
            itemBuilder: (context, index) {
              final item = widget.order['items'][index];
              final itemId = item['id'] as String;
              final billAssignment = billAssignments[itemId] ?? 0;

              final billColors = [
                Colors.blue,
                Colors.green,
                Colors.orange,
                Colors.purple,
                Colors.teal,
              ];

              return Container(
                decoration: BoxDecoration(
                  color: billAssignment > 0
                      ? billColors[(billAssignment - 1) % billColors.length]
                          .withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Item name and basic info
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              item['name'],
                              style: GoogleFonts.dmSans(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          if (!isSplittingBill)
                            Text(
                              '\$${(item['price'] * item['quantity']).toStringAsFixed(2)}',
                              style: GoogleFonts.dmSans(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: 4),

                      // Quantity and base price
                      Text(
                        'Quantity: ${item['quantity']} • \$${item['price']} each',
                        style: GoogleFonts.dmSans(
                          color: Colors.grey[400],
                          fontSize: 14,
                        ),
                      ),

                      // Customization details
                      if (item['customization'] != null) ...[
                        SizedBox(height: 8),
                        _buildCustomizationDetails(item['customization']),
                      ],

                      // Split bill controls
                      if (isSplittingBill) ...[
                        SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '\$${(item['price'] * item['quantity']).toStringAsFixed(2)}',
                              style: GoogleFonts.dmSans(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Wrap(
                              spacing: 8,
                              children: List.generate(numberOfBills, (index) {
                                final billColors = [
                                  Colors.blue,
                                  Colors.green,
                                  Colors.orange,
                                  Colors.purple,
                                  Colors.teal,
                                ];
                                final color =
                                    billColors[index % billColors.length];

                                return _buildBillRadioButton(
                                  itemId,
                                  index + 1,
                                  color,
                                  'Bill ${index + 1}',
                                );
                              }),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCustomizationDetails(Map<String, dynamic> customization) {
    List<Widget> customizationWidgets = [];

    // Allergies
    if (customization['allergies'] != null &&
        (customization['allergies'] as List).isNotEmpty) {
      customizationWidgets.add(
        _buildCustomizationChip(
          'Allergies',
          (customization['allergies'] as List).map((a) => a['name']).join(', '),
          Colors.red,
          Icons.warning,
        ),
      );
    }

    // Spiciness
    if (customization['spiciness'] != null &&
        customization['spiciness'].toString().isNotEmpty) {
      customizationWidgets.add(
        _buildCustomizationChip(
          'Spiciness',
          customization['spiciness'].toString(),
          Colors.orange,
          Icons.local_fire_department,
        ),
      );
    }

    // Dish Addons
    if (customization['dishAddons'] != null &&
        (customization['dishAddons'] as List).isNotEmpty) {
      final addons = (customization['dishAddons'] as List)
          .map((a) => '${a['name']} (${a['quantity']}x)')
          .join(', ');
      customizationWidgets.add(
        _buildCustomizationChip(
          'Addons',
          addons,
          Colors.green,
          Icons.add_circle,
        ),
      );
    }

    // Dish Extras
    if (customization['dishExtras'] != null &&
        (customization['dishExtras'] as List).isNotEmpty) {
      final extras = (customization['dishExtras'] as List)
          .map((e) => '${e['name']} (${e['quantity']}x)')
          .join(', ');
      customizationWidgets.add(
        _buildCustomizationChip(
          'Extras',
          extras,
          Colors.blue,
          Icons.add_box,
        ),
      );
    }

    // Notes
    if (customization['notes'] != null &&
        customization['notes'].toString().isNotEmpty) {
      customizationWidgets.add(
        _buildCustomizationChip(
          'Notes',
          customization['notes'].toString(),
          Colors.purple,
          Icons.note,
        ),
      );
    }

    if (customizationWidgets.isEmpty) {
      return SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Customizations:',
          style: GoogleFonts.dmSans(
            color: Colors.grey[300],
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: customizationWidgets,
        ),
      ],
    );
  }

  Widget _buildCustomizationChip(
      String label, String value, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          SizedBox(width: 4),
          Text(
            '$label: ',
            style: GoogleFonts.dmSans(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.dmSans(
                color: Colors.grey[300],
                fontSize: 11,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillRadioButton(
      String itemId, int billNumber, Color color, String tooltip) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: () {
          setState(() {
            if (billAssignments[itemId] == billNumber) {
              billAssignments[itemId] = 0;
            } else {
              billAssignments[itemId] = billNumber;
            }
            _updateBillTotals();
          });
        },
        child: Container(
          padding: EdgeInsets.all(4),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: billAssignments[itemId] == billNumber
                ? color
                : Colors.grey.withOpacity(0.2),
            border: Border.all(
              color: color.withOpacity(0.5),
              width: 2,
            ),
          ),
          child: billAssignments[itemId] == billNumber
              ? Icon(Icons.check, size: 16, color: Colors.white)
              : SizedBox(width: 16, height: 16),
        ),
      ),
    );
  }

  String _generateBillSummary() {
    List<String> billSummaries = [];
    for (int i = 0; i < numberOfBills; i++) {
      billSummaries
          .add('Bill ${i + 1}: \$${billAmounts[i].toStringAsFixed(2)}');
    }
    return billSummaries.join('\n');
  }

  Widget _buildPaymentSection() {
    double calculateTotal() {
      final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
        final price = (item['price'] ?? 0.0) as double;
        final quantity = (item['quantity'] ?? 1) as int;
        return sum + (price * quantity);
      });

      final tax = subtotal * 0.1; // 10% tax
      final total =
          subtotal + tax + tipAmount; // tipAmount should be a class variable

      return total;
    }

    final total = calculateTotal();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildPaymentOption(
                'Cash',
                Icons.payments_outlined,
                Colors.green,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildPaymentOption(
                'Card',
                Icons.credit_card_outlined,
                Colors.blue,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildPaymentOption(
                'Split',
                Icons.call_split_outlined,
                Colors.orange,
              ),
            ),
          ],
        ),
        if (selectedPaymentMethod == 'Split') ...[
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Cash Amount',
                    labelStyle: TextStyle(color: Colors.white70),
                    prefixIcon:
                        Icon(Icons.payments_outlined, color: Colors.green),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white24),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.green),
                    ),
                  ),
                  style: TextStyle(color: Colors.white),
                  onChanged: (value) {
                    setState(() {
                      cashAmount = double.tryParse(value) ?? 0;
                      cardAmount = total - cashAmount;
                    });
                  },
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: TextField(
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Card Amount',
                    labelStyle: TextStyle(color: Colors.white70),
                    prefixIcon:
                        Icon(Icons.credit_card_outlined, color: Colors.blue),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white24),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.blue),
                    ),
                  ),
                  style: TextStyle(color: Colors.white),
                  onChanged: (value) {
                    setState(() {
                      cardAmount = double.tryParse(value) ?? 0;
                      cashAmount = total - cardAmount;
                    });
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Split Amount:',
                style: GoogleFonts.dmSans(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              Text(
                '\$${(cashAmount + cardAmount).toStringAsFixed(2)} / \$${total.toStringAsFixed(2)}',
                style: GoogleFonts.dmSans(
                  color: (cashAmount + cardAmount) == total
                      ? Colors.green
                      : Colors.orange,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildTipSection(double subtotal) {
    return Column(
      children: [
        Text(
          "Add Tip",
          style: GoogleFonts.dmSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 12),
        Row(
          children: [
            _buildTipButton('10%', subtotal * 0.1),
            SizedBox(width: 8),
            _buildTipButton('15%', subtotal * 0.15),
            SizedBox(width: 8),
            _buildTipButton('20%', subtotal * 0.2),
            SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: tipController,
                keyboardType: TextInputType.number,
                style: GoogleFonts.dmSans(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Custom',
                  hintStyle: GoogleFonts.dmSans(color: Colors.grey),
                  prefixText: '\$ ',
                  prefixStyle: GoogleFonts.dmSans(color: Colors.white),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[700]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.white),
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    tipAmount = double.tryParse(value) ?? 0;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBillSummary(double subtotal, double tax, double total) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildBillRow('Subtotal', subtotal),
          SizedBox(height: 8),
          _buildBillRow('Tax (10%)', tax),
          SizedBox(height: 8),
          _buildBillRow('Tip', tipAmount),
          Divider(color: Colors.grey[600], height: 24),
          _buildBillRow('Total', total, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildActionButtons(double total) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        border: Border(top: BorderSide(color: Colors.grey[800]!)),
      ),
      child: Row(
        children: [
          if (!isSplittingBill) ...[
            Expanded(
              child: ElevatedButton.icon(
                icon: Icon(Icons.call_split, color: Colors.white),
                label: Text(
                  "Split Bill",
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: () {
                  setState(() {
                    isSplittingBill = true;
                  });
                },
              ),
            ),
            SizedBox(width: 12),
          ],
          Expanded(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[700],
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () => Navigator.pop(context),
              child: Text(
                "Cancel",
                style: GoogleFonts.dmSans(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF2CBF5A),
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: isSplittingBill && billAssignments.isEmpty
                  ? null
                  : () => _processPayment(context, total),
              child: Text(
                isSplittingBill ? "Continue Split Payment" : "Process Payment",
                style: GoogleFonts.dmSans(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOption(String title, IconData icon, Color color) {
    final isSelected = selectedPaymentMethod == title;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedPaymentMethod = title;
          if (title == 'Split') {
            _showSplitAmountDialog();
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.grey[800],
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey[400],
              size: 28,
            ),
            SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.dmSans(
                color: isSelected ? color : Colors.grey[400],
                fontWeight: FontWeight.bold,
              ),
            ),
            // Show split amounts if this is the split option and amounts are configured
            if (title == 'Split' &&
                isSelected &&
                cashAmount > 0 &&
                cardAmount > 0) ...[
              SizedBox(height: 4),
              Text(
                'Cash: \$${cashAmount.toStringAsFixed(2)}',
                style: GoogleFonts.dmSans(
                  color: Colors.green,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'Card: \$${cardAmount.toStringAsFixed(2)}',
                style: GoogleFonts.dmSans(
                  color: Colors.blue,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTipButton(String label, double amount) {
    final isSelected = tipAmount == amount;
    return GestureDetector(
      onTap: () {
        setState(() {
          tipAmount = amount;
          tipController.text = amount.toStringAsFixed(2);
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Color(0xFF2CBF5A).withOpacity(0.2)
              : Colors.grey[800],
          border: Border.all(
            color: isSelected ? Color(0xFF2CBF5A) : Colors.transparent,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          style: GoogleFonts.dmSans(
            color: isSelected ? Color(0xFF2CBF5A) : Colors.grey[400],
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildBillRow(String label, double amount, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.dmSans(
            color: Colors.grey[400],
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          '\$${amount.toStringAsFixed(2)}',
          style: GoogleFonts.dmSans(
            color: isTotal ? Colors.white : Colors.grey[400],
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  // Widget _buildPaymentSection() {
  //   double calculateTotal() {
  //     final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
  //       final price = (item['price'] ?? 0.0) as double;
  //       final quantity = (item['quantity'] ?? 1) as int;
  //       return sum + (price * quantity);
  //     });
  //
  //     final tax = subtotal * 0.1; // 10% tax
  //     final total =
  //         subtotal + tax + tipAmount; // tipAmount should be a class variable
  //
  //     return total;
  //   }
  //
  //   final total = calculateTotal();
  //
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.stretch,
  //     children: [
  //       Row(
  //         children: [
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Cash',
  //               Icons.payments_outlined,
  //               Colors.green,
  //             ),
  //           ),
  //           SizedBox(width: 12),
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Card',
  //               Icons.credit_card_outlined,
  //               Colors.blue,
  //             ),
  //           ),
  //           SizedBox(width: 12),
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Split',
  //               Icons.call_split_outlined,
  //               Colors.orange,
  //             ),
  //           ),
  //         ],
  //       ),
  //       if (selectedPaymentMethod == 'Split') ...[
  //         SizedBox(height: 16),
  //         Row(
  //           children: [
  //             Expanded(
  //               child: TextField(
  //                 keyboardType: TextInputType.number,
  //                 decoration: InputDecoration(
  //                   labelText: 'Cash Amount',
  //                   labelStyle: TextStyle(color: Colors.white70),
  //                   prefixIcon:
  //                       Icon(Icons.payments_outlined, color: Colors.green),
  //                   enabledBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.white24),
  //                   ),
  //                   focusedBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.green),
  //                   ),
  //                 ),
  //                 style: TextStyle(color: Colors.white),
  //                 onChanged: (value) {
  //                   setState(() {
  //                     cashAmount = double.tryParse(value) ?? 0;
  //                     cardAmount = total - cashAmount;
  //                   });
  //                 },
  //               ),
  //             ),
  //             SizedBox(width: 12),
  //             Expanded(
  //               child: TextField(
  //                 keyboardType: TextInputType.number,
  //                 decoration: InputDecoration(
  //                   labelText: 'Card Amount',
  //                   labelStyle: TextStyle(color: Colors.white70),
  //                   prefixIcon:
  //                       Icon(Icons.credit_card_outlined, color: Colors.blue),
  //                   enabledBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.white24),
  //                   ),
  //                   focusedBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.blue),
  //                   ),
  //                 ),
  //                 style: TextStyle(color: Colors.white),
  //                 onChanged: (value) {
  //                   setState(() {
  //                     cardAmount = double.tryParse(value) ?? 0;
  //                     cashAmount = total - cardAmount;
  //                   });
  //                 },
  //               ),
  //             ),
  //           ],
  //         ),
  //         SizedBox(height: 8),
  //         Row(
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           children: [
  //             Text(
  //               'Total Split Amount:',
  //               style: GoogleFonts.dmSans(
  //                 color: Colors.white70,
  //                 fontSize: 14,
  //               ),
  //             ),
  //             Text(
  //               '\$${(cashAmount + cardAmount).toStringAsFixed(2)} / \$${total.toStringAsFixed(2)}',
  //               style: GoogleFonts.dmSans(
  //                 color: (cashAmount + cardAmount) == total
  //                     ? Colors.green
  //                     : Colors.orange,
  //                 fontSize: 14,
  //                 fontWeight: FontWeight.bold,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ],
  //     ],
  //   );
  // }

  Future<void> _processPayment(BuildContext context, double total) async {
    if (selectedPaymentMethod == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please select a payment method')),
      );
      return;
    }

    setState(() {
      isProcessingPayment = true;
    });

    try {
      await Future.delayed(Duration(seconds: 2));

      Map<String, dynamic> paymentDetails = {
        'method': selectedPaymentMethod,
        'total': total,
        'timestamp': DateTime.now().toIso8601String(),
      };

      if (isSplittingBill) {
        Map<String, double> splitBills = {};
        for (int i = 0; i < numberOfBills; i++) {
          splitBills['bill${i + 1}'] = billAmounts[i];
        }
        paymentDetails['splitBills'] = splitBills;
        paymentDetails['numberOfBills'] = numberOfBills;
      } else if (selectedPaymentMethod == 'Split') {
        paymentDetails['cashAmount'] = cashAmount;
        paymentDetails['cardAmount'] = cardAmount;
      }

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Color(0xFF2CBF5A)),
              SizedBox(width: 8),
              Text(
                'Payment Successful',
                style: GoogleFonts.dmSans(color: Colors.white),
              ),
            ],
          ),
          content: Text(
            isSplittingBill
                ? 'Payment processed successfully!\n${_generateBillSummary()}'
                : selectedPaymentMethod == 'Split'
                    ? 'Payment processed successfully!\nCash: \$${cashAmount.toStringAsFixed(2)}\nCard: \$${cardAmount.toStringAsFixed(2)}'
                    : 'Payment processed successfully!',
            style: GoogleFonts.dmSans(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                GoRouter.of(context).pop();
              },
              child: Text(
                'Done',
                style: GoogleFonts.dmSans(
                  color: Color(0xFF2CBF5A),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      );
    } finally {
      setState(() {
        isProcessingPayment = false;
      });
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'ready':
        return Color(0xFF2CBF5A);
      case 'cooking':
        return Colors.orange;
      case 'in progress':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _showSplitAmountDialog() {
    final TextEditingController cashController = TextEditingController();
    final TextEditingController cardController = TextEditingController();

    // Initialize with current values if they exist
    if (cashAmount > 0) {
      cashController.text = cashAmount.toStringAsFixed(2);
    }
    if (cardAmount > 0) {
      cardController.text = cardAmount.toStringAsFixed(2);
    }

    double calculateTotal() {
      final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
        final price = (item['price'] ?? 0.0) as double;
        final quantity = (item['quantity'] ?? 1) as int;
        return sum + (price * quantity);
      });

      final tax = subtotal * 0.1; // 10% tax
      final total =
          subtotal + tax + tipAmount; // tipAmount should be a class variable

      return total;
    }

    final double total = calculateTotal();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Split Payment',
          style: GoogleFonts.dmSans(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Total Amount: \$${total.toStringAsFixed(2)}',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
            SizedBox(height: 16),
            TextField(
              controller: cashController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Cash Amount',
                labelStyle: TextStyle(color: Colors.white70),
                prefixIcon: Icon(Icons.payments_outlined, color: Colors.green),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.green),
                ),
              ),
              style: TextStyle(color: Colors.white),
              onChanged: (value) {
                double cash = double.tryParse(value) ?? 0;
                cardController.text = (total - cash).toStringAsFixed(2);
              },
            ),
            SizedBox(height: 12),
            TextField(
              controller: cardController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Card Amount',
                labelStyle: TextStyle(color: Colors.white70),
                prefixIcon:
                    Icon(Icons.credit_card_outlined, color: Colors.blue),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
              style: TextStyle(color: Colors.white),
              onChanged: (value) {
                double card = double.tryParse(value) ?? 0;
                cashController.text = (total - card).toStringAsFixed(2);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
            ),
            onPressed: () {
              final cash = double.tryParse(cashController.text) ?? 0;
              final card = double.tryParse(cardController.text) ?? 0;
              final totalSplit = cash + card;

              // Validate that the split amounts equal the total
              if ((totalSplit - total).abs() > 0.01) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Split amounts must equal total amount (\$${total.toStringAsFixed(2)})'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              setState(() {
                cashAmount = cash;
                cardAmount = card;
              });

              // Show confirmation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Split payment configured: Cash \$${cash.toStringAsFixed(2)}, Card \$${card.toStringAsFixed(2)}'),
                  backgroundColor: Colors.green,
                ),
              );

              Navigator.pop(context);
            },
            child: Text(
              'Confirm',
              style: GoogleFonts.dmSans(),
            ),
          ),
        ],
      ),
    );
  }
}

// Filter Options Modal
class FilterOptionsModal extends StatefulWidget {
  final Function(Map<String, dynamic>) onApply;

  const FilterOptionsModal({super.key, required this.onApply});

  @override
  State<FilterOptionsModal> createState() => _FilterOptionsModalState();
}

class _FilterOptionsModalState extends State<FilterOptionsModal> {
  List<String> selectedStatuses = [];
  String? sortBy;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Filter Orders",
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text(
            "Status",
            style: GoogleFonts.dmSans(
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip("Ready", "Ready"),
              _buildFilterChip("In Progress", "In Progress"),
              _buildFilterChip("Cooking", "Cooking"),
              _buildFilterChip("Cancelled", "Cancelled"),
              _buildFilterChip("Completed", "Completed"),
            ],
          ),
          SizedBox(height: 16),
          Text(
            "Sort by",
            style: GoogleFonts.dmSans(
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          Wrap(
            spacing: 8,
            children: [
              _buildSortChip("Newest First", "newest"),
              _buildSortChip("Oldest First", "oldest"),
              _buildSortChip("Table Number", "table"),
            ],
          ),
          SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text("Cancel"),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    widget.onApply({
                      'statuses': selectedStatuses,
                      'sortBy': sortBy,
                    });
                    Navigator.pop(context);
                  },
                  child: Text("Apply"),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = selectedStatuses.contains(value);
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.dmSans(
          color: isSelected ? Colors.white : Colors.black,
        ),
      ),
      selected: isSelected,
      backgroundColor: Colors.grey[200],
      selectedColor: Colors.blue,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            selectedStatuses.add(value);
          } else {
            selectedStatuses.remove(value);
          }
        });
      },
    );
  }

  Widget _buildSortChip(String label, String value) {
    final isSelected = sortBy == value;
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.dmSans(
          color: isSelected ? Colors.white : Colors.black,
        ),
      ),
      selected: isSelected,
      backgroundColor: Colors.grey[200],
      selectedColor: Colors.green,
      onSelected: (selected) {
        setState(() {
          sortBy = selected ? value : null;
        });
      },
    );
  }
}

// Add a new PaymentModal widget for handling individual bill payments
class PaymentModal extends StatefulWidget {
  final double total;
  final String billLabel;

  const PaymentModal({
    required this.total,
    required this.billLabel,
    Key? key,
  }) : super(key: key);

  @override
  _PaymentModalState createState() => _PaymentModalState();
}

class _PaymentModalState extends State<PaymentModal> {
  String? selectedPaymentMethod;

  @override
  Widget build(BuildContext context) {
    throw UnimplementedError();
  }

  // Implement the payment modal UI here...
}
